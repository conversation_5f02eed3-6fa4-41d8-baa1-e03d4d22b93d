# LangGraph AI Assistant Analysis

## Overview

The `src\outbond_ai_assistant` directory implements a sophisticated ReAct (Reasoning and Acting) agent using LangGraph for Outbond's sales development platform. This agent serves as an AI Sales Development Representative (SDR) that helps users manage outbound campaigns within Outbond tables.

## Architecture Overview

```mermaid
graph TD
    A[User Input] --> B[Agent Node]
    B --> C{Tool Selection}
    C -->|Web Research| D[Search Tools]
    C -->|Data Operations| E[Table Tools]
    C -->|LinkedIn Operations| F[LinkedIn Tools]
    C -->|Content Generation| G[AI Content Tools]
    D --> H[Tools Node]
    E --> H
    F --> H
    G --> H
    H --> I{Continue?}
    I -->|Yes| B
    I -->|No| J[End]
```

## Core Components

### 1. Graph Structure (`graph.py`)

The main graph follows the ReAct pattern with these key nodes:

- **Agent Node**: Makes decisions and calls tools
- **Tools Node**: Executes selected tools
- **Human-in-the-loop**: Supports interrupts for user input

```python
def create_graph():
    workflow = StateGraph(AgentState)
    
    # Add agent node
    workflow.add_node("agent", agent)
    
    # Add tools node  
    workflow.add_node("tools", tool_node)
    
    # Add conditional edges
    workflow.add_conditional_edges(
        "agent",
        should_continue,
        {"continue": "tools", "end": END}
    )
```

### 2. State Management (`state.py`)

The agent maintains state through `AgentState` which includes:
- Message history
- Current table context
- Configuration settings
- Tool execution results

### 3. System Prompt and Tool Selection

The system prompt in `prompts.py` defines the agent's behavior and tool selection logic:

```python
OUTBOND_AI_ASSISTANT_PROMPT = """You are Bond AI, an AI Sales Development Representative (SDR) embedded in Outbond's no-code workspace. Your role is to help the USER efficiently manage outbound campaigns within a single Outbond table.

**Tasks:**
* Generate outreach campaigns.
* Add, update, or edit enrichment columns.
* Modify table columns, rows, or workflows as needed.
```

## Tool Categories and Specifications

### 1. Web Research Tools

#### `search(query, max_results=5)`
- **Purpose**: General web search using Tavily
- **Scope**: Current events, general information
- **Usage**: When user needs external information

#### `scrape_website(url)`
- **Purpose**: Extract content from specific URLs
- **Scope**: Single webpage content extraction
- **Usage**: When specific website content is needed

### 2. Table Data Management Tools

#### `read_table_data(max_rows, column_ids, filters, sorts, search, summarize)`
- **Purpose**: Read and optionally summarize table data
- **Scope**: Current user's table view with filters/sorts
- **Key Features**:
  - Respects user's current filters and sorts
  - Optional AI-powered summarization
  - Token-efficient data retrieval

```mermaid
graph LR
    A[User Request] --> B{Need Table Data?}
    B -->|Yes| C[Apply User Filters]
    C --> D[Apply Sorts/Search]
    D --> E{Summarize?}
    E -->|Yes| F[AI Summarization]
    E -->|No| G[Raw Data]
    F --> H[Return Summary]
    G --> H
```

#### Filter System
The tool supports complex filtering with nested conditions:

```python
class Filter(BaseModel):
    column_id: int  # 1-indexed
    operator: Operator  # eq, neq, lt, contains, etc.
    value: Optional[str]

class FilterGroup(BaseModel):
    operator: GroupOperator  # AND/OR
    rules: List[Union[Filter, FilterGroup]]  # Supports nesting
```

### 3. LinkedIn Integration Tools

#### `upsert_linkedin_person_profile_column_from_url(column_name, linkedin_url)`
- **Purpose**: Create/update LinkedIn person profile columns
- **Scope**: Individual LinkedIn profiles
- **Process**: URL → Profile data → Column creation

#### `upsert_linkedin_company_profile_column_from_url(column_name, linkedin_url)`
- **Purpose**: Create/update LinkedIn company profile columns
- **Scope**: Company LinkedIn profiles

#### `search_linkedin_profiles(search_request)`
- **Purpose**: Search for LinkedIn profiles with filters
- **Scope**: Advanced LinkedIn search with multiple criteria
- **Features**: Location, industry, company size filters

### 4. Data Enrichment Tools

#### `upsert_phone_number_column(column_name)`
- **Purpose**: Add phone number enrichment
- **Scope**: Contact data enhancement

#### `upsert_work_email_column(column_name)`
- **Purpose**: Add work email enrichment
- **Scope**: Professional email discovery

### 5. AI Content Generation Tools

#### `upsert_ai_text_column(column_name, prompt)`
- **Purpose**: Generate AI-powered text content
- **Scope**: Custom text generation based on row data

#### `upsert_ai_message_copywriter(column_name, prompt)`
- **Purpose**: Generate sales/marketing copy
- **Scope**: Outreach message creation
- **Enhancement**: Uses specialized copywriting prompts

#### `upsert_bond_ai_researcher_column(column_name, prompt)`
- **Purpose**: Research-focused content generation
- **Scope**: In-depth research and analysis
- **Enhancement**: Uses research-specific prompts

### 6. Column Management Tools

#### `run_column(column_id, row_ids)`
- **Purpose**: Execute runnable columns on specific rows
- **Scope**: Batch processing of column operations

#### `upsert_text_column(column_name, default_value)`
- **Purpose**: Create simple text columns
- **Scope**: Basic data entry columns

## Tool Selection Logic

### 1. Prompt-Driven Selection

The system prompt guides tool selection through:

```markdown
<intelligent_data_access>
**Summary-Driven Workflow:**
* Always align actions with the USER's current table view (filters, sorts, searches).
* Perform 95% of tasks using only the summary. Directly query data (`read_table_data`) only if critical information is missing.
</intelligent_data_access>
```

### 2. Context-Aware Decision Making

The agent considers:
- **Current table state**: Filters, sorts, search terms
- **User intent**: Research, enrichment, content generation
- **Data requirements**: Summary vs. detailed data
- **Tool capabilities**: Matching user needs to tool functions

### 3. Tool Selection Flow

```mermaid
graph TD
    A[User Request] --> B{Analyze Intent}
    B -->|Research| C[Web Tools]
    B -->|Data Query| D[Table Tools]
    B -->|Enrichment| E[LinkedIn/Contact Tools]
    B -->|Content| F[AI Generation Tools]
    
    C --> G{Need External Data?}
    G -->|General| H[search]
    G -->|Specific URL| I[scrape_website]
    
    D --> J{Need Summary?}
    J -->|Yes| K[read_table_data + summarize]
    J -->|No| L[read_table_data]
    
    E --> M{LinkedIn or Contact?}
    M -->|LinkedIn| N[LinkedIn Tools]
    M -->|Contact| O[Phone/Email Tools]
    
    F --> P{Content Type?}
    P -->|General| Q[upsert_ai_text_column]
    P -->|Sales Copy| R[upsert_ai_message_copywriter]
    P -->|Research| S[upsert_bond_ai_researcher_column]
```

## Advanced Features

### 1. AI-Powered Summarization

The `read_table_data` tool includes intelligent summarization:

```python
if summarize:
    model = load_chat_model_non_thinking("openai/gpt-4o-mini").with_structured_output(TableSummaryOutput)
    
    table_data_str = json.dumps(table_data, indent=2, default=str)
    prompt_message = SystemMessage(TABLE_SUMMARY_PROMPT)
    data_message = HumanMessage(table_data_str)
    
    summary_response = model.invoke([prompt_message, data_message], config)
    return summary_response, None
```

### 2. Prompt Enhancement

AI content tools use prompt enhancement for better results:

```python
# Enhance the prompt using the model
model = load_chat_model_non_thinking(configuration.model)
messages = [
    SystemMessage(BOND_AI_RESEARCHER_COLUMN_PROMPT),
    HumanMessage(prompt)
]
enhanced_prompt_message = model.invoke(messages, config)
enhanced_prompt = enhanced_prompt_message.content
```

### 3. Stream Writing for User Feedback

Tools provide real-time feedback through stream writing:

```python
stream_writer = get_stream_writer()
stream_writer({"custom_tool_call": f"Searching the web for {query}"})
```

## Configuration and Models

### Model Selection
- **Primary**: Configurable through `Configuration` class
- **Summarization**: `openai/gpt-4o-mini` for efficiency
- **Enhancement**: Uses non-thinking models for prompt enhancement

### Error Handling
- Graceful fallbacks for AI operations
- Comprehensive error messages
- Continuation on non-critical failures

## Integration Points

### 1. Database Layer (`agent_db.py`)
- Supabase integration for data persistence
- Table and column management
- Row-level operations

### 2. External APIs
- **Tavily**: Web search
- **FireCrawl**: Website scraping
- **LinkedIn APIs**: Profile data
- **CrustData**: Contact enrichment

### 3. LangSmith Integration
- Prompt management
- Model orchestration
- Performance monitoring

## Usage Patterns

### 1. Research Workflow
```
User: "Research competitors for SaaS companies"
→ search("SaaS competitors")
→ scrape_website(relevant_urls)
→ upsert_bond_ai_researcher_column("Competitor Analysis", enhanced_prompt)
```

### 2. Enrichment Workflow
```
User: "Add LinkedIn profiles for all contacts"
→ read_table_data(summarize=True)
→ upsert_linkedin_person_profile_column_from_url("LinkedIn Profile", url)
→ run_column(column_id, row_ids)
```

### 3. Content Generation Workflow
```
User: "Create personalized outreach messages"
→ read_table_data(relevant_columns)
→ upsert_ai_message_copywriter("Outreach Message", enhanced_prompt)
→ run_column(column_id, all_rows)
```

## Performance Considerations

1. **Token Efficiency**: Summarization reduces token usage by 80-90%
2. **Batch Operations**: Column operations support batch processing
3. **Lazy Loading**: Data fetched only when needed
4. **Caching**: Results cached at database level

## Security Features

1. **Input Validation**: All inputs validated through Pydantic models
2. **Injection Protection**: SQL injection prevention
3. **Rate Limiting**: API rate limiting for external services
4. **Access Control**: User-scoped data access

## Complete Tool Reference

### Web Research Tools
| Tool | Purpose | Input | Output |
|------|---------|-------|--------|
| `search` | Web search via Tavily | query, max_results | Search results |
| `scrape_website` | Extract webpage content | url | Markdown content |

### Table Management Tools
| Tool | Purpose | Input | Output |
|------|---------|-------|--------|
| `read_table_data` | Read/summarize table data | filters, sorts, columns | Data/summary |
| `read_user_view_table_filters` | Get current filters | - | Filter configuration |
| `update_user_view_table_filters_tool` | Update table filters | filters | Success/error |

### LinkedIn Tools
| Tool | Purpose | Input | Output |
|------|---------|-------|--------|
| `upsert_linkedin_person_profile_column_from_url` | Person profile column | name, url | Column creation |
| `upsert_linkedin_company_profile_column_from_url` | Company profile column | name, url | Column creation |
| `search_linkedin_profiles` | Search LinkedIn profiles | filters, page | Profile results |

### Enrichment Tools
| Tool | Purpose | Input | Output |
|------|---------|-------|--------|
| `upsert_phone_number_column` | Phone enrichment | name, linkedin_url | Column creation |
| `upsert_work_email_column` | Email enrichment | name, full_name, company | Column creation |

### AI Content Tools
| Tool | Purpose | Input | Output |
|------|---------|-------|--------|
| `upsert_ai_text_column` | AI text generation | name, prompt | Column creation |
| `upsert_ai_message_copywriter` | Sales copy generation | name, prompt | Column creation |
| `upsert_bond_ai_researcher_column` | Research content | name, prompt | Column creation |

### Column Operations
| Tool | Purpose | Input | Output |
|------|---------|-------|--------|
| `run_column` | Execute column | column_id, count | Execution status |
| `upsert_text_column` | Simple text column | name, formula | Column creation |

## System Architecture Diagram

```mermaid
graph TB
    subgraph "User Interface"
        UI[User Input]
    end
    
    subgraph "LangGraph Agent"
        A[Agent Node]
        T[Tools Node]
        S[State Management]
    end
    
    subgraph "Tool Categories"
        W[Web Tools]
        D[Data Tools]
        L[LinkedIn Tools]
        E[Enrichment Tools]
        AI[AI Content Tools]
        C[Column Tools]
    end
    
    subgraph "External Services"
        TV[Tavily Search]
        FC[FireCrawl]
        LI[LinkedIn API]
        CD[CrustData]
        OAI[OpenAI]
    end
    
    subgraph "Database Layer"
        SB[Supabase]
        RD[Redis Cache]
    end
    
    UI --> A
    A --> T
    T --> W
    T --> D
    T --> L
    T --> E
    T --> AI
    T --> C
    
    W --> TV
    W --> FC
    L --> LI
    E --> CD
    AI --> OAI
    
    D --> SB
    C --> SB
    SB --> RD
    
    T --> S
    S --> A
```

This LangGraph implementation provides a robust, scalable foundation for AI-powered sales development operations with comprehensive tool integration and intelligent decision-making capabilities.