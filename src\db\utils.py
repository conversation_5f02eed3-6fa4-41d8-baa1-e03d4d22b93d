from datetime import datetime
import logging
import mimetypes
from uuid import UUID
import uuid
from supabase import create_async_client as create_client, AsyncClient
from src.core.config import get_settings
from pydantic import BaseModel
from typing import Optional, Dict, Any
import httpx
from enum import Enum
from functools import lru_cache
import json

logger = logging.getLogger("backend_api.db.utils")

settings = get_settings()


def format_date(date_input) -> str:
    """Format a date string or object to YYYY-MM-DD format

    Args:
        date_input: Can be a string, a DateObject with a 'year' attribute, or None

    Returns:
        Formatted date string or empty string if input is None/empty
    """
    if not date_input:
        return ""

    # Handle DateObject (which has year, month, day attributes)
    if hasattr(date_input, "year"):
        try:
            year = getattr(date_input, "year", None)
            month = getattr(date_input, "month", 1)
            day = getattr(date_input, "day", 1)

            if year:
                date_str = f"{year}-{month:02d}-{day:02d}"
                return date_str
            return ""
        except Exception:
            return ""

    # Handle string input
    if isinstance(date_input, str):
        try:
            return datetime.strptime(date_input, "%Y-%m-%d").strftime("%Y-%m-%d")
        except ValueError:
            return date_input

    # If we get here, return empty string
    return ""


column_type_map = {
    "text": 1,
    "date": 2,
    "number": 3,
    "object": 4,
    "array": 5,
    "boolean": 6,
    "linkedin_profile_url": 9,
    "linkedin_company_url": 10,
    "company_domain": 11,
    "full_name": 12,
    "first_name": 13,
    "last_name": 14,
    "linkedin_profile": 15,
    "linkedin_company": 16,
    "company_headcount": 17,
    "company_summary": 18,
    "phone": 7,
    "email": 8,
    "post": 20,
    "interaction": 21,
    "AI": 19,
    "AI_formula": 22,
    "formula": 23,
    "company_name": 24,
    "job_title": 25,
    "http": 26,
    "webhook": 27,
}


def get_type_id(type_name: str) -> int:
    return column_type_map.get(type_name, 1)  # Default to text if not found


def get_type_name(input_value: str | int) -> str:
    # If the input is a string, convert it to a type ID first
    type_id = get_type_id(input_value) if isinstance(input_value, str) else input_value

    # Look up the type name from the dictionary
    for key, value in column_type_map.items():
        if value == type_id:
            return key
    return "text"  # Default if not found


class CellStatus(BaseModel):
    run: Optional[str] = None
    message: Optional[str] = None


class CellId(BaseModel):
    table_id: str
    column_id: int
    row_id: int


class Cell(CellId):
    value: Optional[str] = None
    run_status: Optional[CellStatus] = None
    extras: Optional[Dict[str, Any]] = None
    created_at: Optional[str]
    updated_at: Optional[str]
    created_by: Optional[str]
    updated_by: Optional[str]


class CellDetails(CellId):
    value: Optional[Any] = None


class RealtimeEvent(str, Enum):
    CELL_UPDATE = "CELL_UPDATE"
    ROW_INSERT = "ROW_INSERT"


async def get_supabase_client() -> AsyncClient:
    """
    Initialize and return a Supabase client instance.
    Uses environment variables for configuration.
    """
    try:
        return await create_client(
            settings.SUPABASE_URL, settings.SUPABASE_SERVICE_ROLE_KEY.get_secret_value()
        )
    except Exception as e:
        logger.error(f"Failed to initialize Supabase client: {str(e)}")
        raise


async def get_cell(supabase: AsyncClient, cell_id: CellId) -> Cell:
    try:
        cell = (
            await supabase.table("cells")
            .select("*")
            .eq("column_id", cell_id.column_id)
            .eq("row_id", cell_id.row_id)
            .eq("table_id", cell_id.table_id)
            .execute()
        )

        if cell.data and len(cell.data) > 0:
            return Cell(**cell.data[0])
        else:
            raise HTTPException(status_code=404, detail="Cell not found")
    except Exception as e:
        logger.error(f"Failed to retrieve cell: {str(e)}")
        raise


async def update_cell(supabase: AsyncClient, cell_data: Cell) -> Cell:
    try:
        cell = (
            await supabase.table("cells")
            .update(cell_data.model_dump())
            .eq("column_id", cell_data.column_id)
            .eq("row_id", cell_data.row_id)
            .eq("table_id", cell_data.table_id)
            .execute()
        )
        return Cell(**cell.data[0])
    except Exception as e:
        logger.error(f"Failed to update cell: {str(e)}")
        raise


async def upsert_cell_details(
    supabase: AsyncClient, cell_details: CellDetails
) -> CellDetails:
    try:
        cell_details = (
            await supabase.table("cell_details")
            .upsert(cell_details.model_dump(), on_conflict="table_id,column_id,row_id")
            .execute()
        )
        return CellDetails(**cell_details.data[0])
    except Exception as e:
        logger.error(f"Failed to upsert cell details: {str(e)}")
        raise


async def reimburse_tokens(
    supabase: AsyncClient, organization_id: str, tokens: int
) -> None:
    try:
        await supabase.rpc(
            "modify_tokens",
            {
                "p_operation": "increase",
                "p_organization_id": organization_id,
                "p_tokens": tokens,
            },
        ).execute()
    except Exception as e:
        logger.error(f"Failed to reimburse tokens: {str(e)}")
        raise


async def send_realtime_broadcast(
    supabase: AsyncClient, channel: str, event: RealtimeEvent, payload: Dict[str, Any]
) -> None:
    try:
        response = await supabase.rpc(
            "notify_fe_generic",
            {
                "channel": channel,
                "event": event,
                "data": payload,
            },
        ).execute()
    except Exception as e:
        logger.error(f"Failed to send realtime broadcast: {str(e)}")
        raise


async def update_cell_and_notify(
    supabase: AsyncClient,
    cell: Cell,
) -> None:
    await update_cell(supabase, cell)
    await send_realtime_broadcast(
        supabase, cell.table_id, RealtimeEvent.CELL_UPDATE, cell.model_dump()
    )


async def download_and_upload_image(
    supabase: AsyncClient,
    image_url: str,
    bucket: str,
    storage_path: str,
    image_name: str = None,
) -> str:
    """
    Download an image from a URL and upload it to Supabase storage asynchronously.

    Args:
        supabase: Supabase AsyncClient instance
        image_url: URL of the image to download
        bucket: Storage bucket name (usually organization_id)
        storage_path: Path within the bucket to store the image
        image_name: Optional custom name for the image file

    Returns:
        Path to the uploaded image in storage.

    Raises:
        Exception with detailed error message if the operation fails.
    """
    import tempfile
    import uuid
    import mimetypes
    from pathlib import Path
    import httpx
    import aiofiles
    import asyncio
    import os
    import logging

    logger = logging.getLogger("src.db.utils")

    try:
        # Download the image
        async with httpx.AsyncClient() as client:
            response = await client.get(image_url, timeout=30.0)
            response.raise_for_status()
            image_data = response.content
            logger.info(f"Downloaded image: {len(image_data)} bytes")

        # Determine MIME type
        content_type = response.headers.get("content-type", "")
        extension = mimetypes.guess_extension(content_type) or ""

        # If no valid extension, infer from URL
        if not extension:
            url_path = Path(image_url.split("?")[0])
            extension = url_path.suffix if url_path.suffix else ".jpg"
            content_type = "image/jpeg" if extension == ".jpg" else content_type

        # Generate filename
        if image_name:
            filename = (
                f"{image_name}{extension}" if "." not in image_name else image_name
            )
        else:
            filename = f"{uuid.uuid4()}{extension}"

        full_path = f"{storage_path}/{filename}"
        logger.info(f"Storage path: {full_path}")

        # Use absolute temporary directory path to avoid getcwd() calls
        temp_dir = "/tmp"  # Use a fixed path that's guaranteed to exist
        temp_file_path = os.path.join(temp_dir, f"{uuid.uuid4()}{extension}")

        logger.info(f"Temporary file path: {temp_file_path}")

        # Write to temporary file
        async with aiofiles.open(temp_file_path, "wb") as f:
            await f.write(image_data)
            logger.info(f"Wrote image data to temporary file")

        try:
            # Read the file content directly
            async with aiofiles.open(temp_file_path, "rb") as f:
                file_content = await f.read()

            logger.info(f"File content read: {len(file_content)} bytes")

            # Upload the file content directly
            upload_response = await supabase.storage.from_(bucket).upload(
                path=full_path,
                file=file_content,
                file_options={
                    "content-type": content_type or "application/octet-stream",
                    "cache-control": "3600",
                    "upsert": "true",
                },
            )

            logger.info(f"Upload successful: {upload_response.path}")
            return upload_response.path

        finally:
            # Use safe file removal that won't block
            try:
                # Use asyncio.to_thread instead of aiofiles.os.remove
                await asyncio.to_thread(os.remove, temp_file_path)
                logger.info("Temporary file removed")
            except Exception as e:
                logger.warning(f"Failed to remove temporary file: {str(e)}")

    except httpx.HTTPStatusError as e:
        logger.error(
            f"HTTP error downloading image: {e.response.status_code} - {e.response.text}"
        )
        raise Exception(f"HTTP error downloading image: {e.response.status_code}")

    except Exception as e:
        logger.error(f"Error downloading or uploading image: {str(e)}")
        raise Exception(f"Error downloading or uploading image: {str(e)}")


async def handle_invalid_input(body: dict[str, Any], err: Any) -> None:
    try:
        supabase_client: AsyncClient = await get_supabase_client()
        cell = await get_cell(
            supabase_client,
            CellId(
                table_id=body["table_id"],
                row_id=body["row_id"],
                column_id=body["column_id"],
            ),
        )
        # Format error message to be more focused and descriptive
        if isinstance(err, list):
            # Handle multiple validation errors
            primary_error = err[0] if err else {}
            error_type = primary_error.get("type", "validation_error")
            field_name = (
                ".".join(primary_error.get("loc", []))
                if primary_error.get("loc")
                else "input"
            )
            error_msg = primary_error.get("msg", "Invalid input")

            # Create a focused message with the most important error first
            focused_message = f"Error: {error_msg} for {field_name}"

            # Add error count if there are multiple errors
            if len(err) > 1:
                focused_message += f" (+{len(err) - 1} more validation errors)"

        else:
            # Handle single error object
            focused_message = f"Invalid input: {err}"

        cell.run_status = CellStatus(run="invalid_input", message=focused_message)
        cell.value = None
        cell.updated_at = datetime.now().isoformat()
        await update_cell_and_notify(supabase_client, cell)
        if body["credits"] > 0:
            await reimburse_tokens(
                supabase_client, body["organization_id"], body["credits"]
            )
    except Exception as e:
        logger.error(f"payload: {json.dumps(body)}")
        logger.error(f"Error downloading or uploading image: {str(e)}")


async def process_webhook_data(webhook_id: str, payload: dict) -> dict:
    """Process webhook data using Supabase RPC"""
    try:
        supabase = await get_supabase_client()
        result = await supabase.rpc(
            "process_webhook_request", {"p_id": webhook_id, "payload": payload}
        ).execute()

        if result.data:
            await send_realtime_broadcast(
                supabase,
                result.data.get("table_id"),
                RealtimeEvent.ROW_INSERT,
                {
                    "table_id": result.data.get("table_id"),
                    "id": result.data.get("row_id"),
                },
            )
            return result.data
        else:
            logger.error(f"RPC call failed: {result}")
            return {"success": False, "error": "Failed to process webhook data"}

    except Exception as e:
        logger.error(f"Error calling Supabase RPC: {e}")
        return {"success": False, "error": str(e)}
