# ./src/agent/webapp.py
import asyncio
import json
import logging
import os
import uuid
from contextlib import asynccontextmanager
from datetime import datetime

import asyncpg
import redis.asyncio as redis
import stripe
from celery import chain
from dotenv import load_dotenv
from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi_limiter import FastAPILimiter
from fastapi_limiter.depends import RateLimiter

from src.core.config import get_settings
from src.db.optimised_utils import (
    get_supabase_client,
    update_organization_subscription,
)
from src.db.utils import (
    handle_invalid_input,
    process_webhook_data,
)
from src.schemas.requests import (
    ExportTableCSVInput,
    ImportCSVInput,
    ServiceRequest,
    ValidateColumnsInput,
)
from src.services.outbond.invite import invite
from src.services.proxycurl.people.person_profile import (
    PersonProfileInput,
    get_linkedin_person_profile,
)
from src.utils.webhook.rate_limiter import (
    WebhookPayload,
    custom_callback,
    ip_identifier,
    webhook_id_identifier,
)
from src.worker.tasks.csv import run_export_table_to_csv_task, run_insert_csv_file_task
from src.worker.tasks.db import reimburse
from src.worker.tasks.email import get_email_finder, get_email_verifier
from src.worker.tasks.http import run_http_task
from src.worker.tasks.lima import run_lima_task
from src.worker.tasks.llm import run_only_if_task
from src.worker.tasks.mobile_phone import get_phone_finder
from src.worker.tasks.openai import run_openai_task
from src.worker.tasks.proxycurl import run_proxycurl_task

# Load environment variables at the very beginning, with overwrite=True
load_dotenv(override=True)

# Configure Stripe with the loaded environment variable
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
webhook_secret = os.getenv("STRIPE_WEBHOOK_SECRET")

if not stripe.api_key or not webhook_secret:
    raise ValueError("Missing required Stripe environment variables")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Global connection objects will be stored in app.state
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize Redis connection
    # app.state.redis = await aioredis.from_url(
    #     os.getenv("REDIS_URL", "redis://localhost:6379"), decode_responses=True
    # )

    # Initialize PostgreSQL connection pool
    app.state.pg_pool = await asyncpg.create_pool(
        os.getenv("POSTGRES_URL", "postgresql://user:password@localhost:5432/postgres")
    )

    # Start background task for listening to PostgreSQL notifications
    # app.state.pg_listener_task = asyncio.create_task(listen_for_db_changes(app))

    logger.info("PostgreSQL connection initialized")

    settings = get_settings()
    redis_connection = redis.from_url(
        settings.CELERY_BROKER_URL,
        encoding="utf8",
    )

    await FastAPILimiter.init(
        redis_connection, prefix="webhook_limiter", http_callback=custom_callback
    )

    yield  # Server is running and handling requests

    # Cleanup on shutdown
    logger.info("Shutting down connections...")
    # app.state.pg_listener_task.cancel()
    # try:
    #     await app.state.pg_listener_task
    # except asyncio.CancelledError:
    #     pass

    # await app.state.redis.close()
    await app.state.pg_pool.close()
    await FastAPILimiter.close()
    logger.info("All connections closed")


# async def listen_for_db_changes(app: FastAPI):
#     """Background task to listen for PostgreSQL notifications and update Redis cache."""
#     conn = await app.state.pg_pool.acquire()
#     try:
#         await conn.execute("LISTEN cache_invalidation;")
#         logger.info("Listening for PostgreSQL cache_invalidation events")
#
#         while True:
#             msg = await conn.fetchrow("SELECT * FROM pg_notification_queue_usage()")
#             if msg:
#                 notification = await conn.fetch("NOTIFY pg_get_notifications()")
#                 for notif in notification:
#                     try:
#                         event_data = json.loads(notif["payload"])
#                         row_id = event_data.get("row_id")
#                         table_id = event_data.get("table_id")
#
#                         if row_id and table_id:
#                             # Refresh Redis cache from PostgreSQL
#                             redis_key = f"row:{row_id}"
#
#                             # Query PostgreSQL for the latest data
#                             row_data = await conn.fetchrow(
#                                 "SELECT * FROM cells WHERE row_id = $1 AND table_id = $2",
#                                 row_id, table_id
#                             )
#
#                             if row_data:
#                                 # Update Redis cache
#                                 await app.state.redis.delete(redis_key)
#                                 for column_id, value in row_data.items():
#                                     if column_id not in ("row_id", "table_id"):
#                                         await app.state.redis.hset(
#                                             redis_key,
#                                             column_id,
#                                             json.dumps({"value": value})
#                                         )
#                                 logger.info(f"Updated Redis cache for row:{row_id}")
#                     except json.JSONDecodeError:
#                         logger.error(f"Failed to parse notification payload: {notif['payload']}")
#                     except Exception as e:
#                         logger.error(f"Error processing notification: {str(e)}")
#
#             # Wait a short time before checking again
#             await asyncio.sleep(0.1)
#     except asyncio.CancelledError:
#         logger.info("PostgreSQL listener task cancelled")
#         raise
#     except Exception as e:
#         logger.error(f"Error in PostgreSQL listener: {str(e)}")
#     finally:
#         await app.state.pg_pool.release(conn)


app = FastAPI(lifespan=lifespan)
# app.include_router(services_router)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    user = request.state.user if hasattr(request.state, "user") else None
    body = exc.body or {}

    # Use pre-instantiated BackgroundTasks from app if possible, otherwise create
    # background_tasks = getattr(request.app, "background_tasks", BackgroundTasks())

    # Log only if absolutely necessary - consider async logging if possible
    # logger.info(f"Request body: {body}")  # Comment out unless critical for debugging

    # if user and {"column_id", "row_id", "table_id", "credits"} <= body.keys():

    await handle_invalid_input(body, exc.errors())
    logger.error(f"Request body: {body}")

    # Precompute response to avoid redundant encoding
    return JSONResponse(
        status_code=422,  # Use literal instead of status.HTTP_422_UNPROCESSABLE_ENTITY
        content={
            "detail": str(exc.errors()),
            "hello": "hello",
        },  # Convert errors to string for JSON serialization
    )


@app.get("/health")
def read_root():
    return {"status": "ok"}


@app.get("/hello")
def read_root():
    return {"Hello": "World"}


@app.post("/validate-columns")
def validate_columns(request: ValidateColumnsInput):
    # TODO implement AI Validate column types
    pass


@app.post("/run-cell")
def run_cell(request: ServiceRequest, background_tasks: BackgroundTasks):
    # Serialize the request to a dict for Celery
    request_dict = request.model_dump()
    # Send to Celery task
    match request_dict["service_id"]:
        case 24:
            background_tasks.add_task(invite, request_dict)
        case 7 | 8 | 9 | 16:
            run_lima_task.delay(request_dict)
        case 17:
            run_proxycurl_task.delay(request_dict)
        case 26:
            run_http_task.delay(request_dict)
        case 1 | 2 | 3 | 4 | 5 | 6 | 12 | 13 | 14 | 18 | 19 | 20 | 21 | 22:
            run_openai_task.delay(request_dict)
        case 10:
            providers = request_dict["providers"]["email_providers"]
            verifiers = request_dict["providers"]["verify_providers"]
            task = chain(
                run_only_if_task.s(None, request_dict),
                # Flatten the nested list comprehension
                *[
                    subtask
                    for provider in providers
                    for subtask in [
                        get_email_finder(provider).s(request_dict),
                        get_email_verifier("millionverifier").s(request_dict),
                    ]
                ],
                reimburse.s(request_dict),
            )()
        case 11:
            providers = request_dict["providers"]["providers"]
            task = chain(
                run_only_if_task.s(None, request_dict),
                *[get_phone_finder(provider).s(request_dict) for provider in providers],
                reimburse.s(request_dict),
            )()

        case _:
            return {"status": "error", "message": "Unknown service_id"}
    return {"status": "processing"}


@app.post("/person-profile")
async def person_profile(request: PersonProfileInput):
    return await get_linkedin_person_profile(request)


@app.post("/import-csv")
def import_csv(request: ImportCSVInput):
    run_insert_csv_file_task.delay(request.model_dump())
    return {"status": "processing"}


@app.post("/export-table-csv")
def export_table_csv(request: ExportTableCSVInput):
    run_export_table_to_csv_task.delay(request.model_dump())
    return {"status": "processing"}


@app.post("/stripe-webhook")
async def stripe_webhook(request: Request):
    payload = await request.body()
    stripe_signature = request.headers.get("stripe-signature")

    try:
        event = stripe.Webhook.construct_event(
            payload, stripe_signature, webhook_secret
        )
    except ValueError:
        logger.error("Invalid payload received")
        raise HTTPException(status_code=400, detail="Invalid payload")
    except stripe.error.SignatureVerificationError:
        logger.error("Invalid signature received")
        raise HTTPException(status_code=400, detail="Invalid signature")

    # Log the full event data
    logger.info(f"Received webhook event type: {event['type']}")

    if event["type"] == "checkout.session.completed":
        try:
            session = event["data"]["object"]

            # Extract session data as before
            customer_details = session["customer_details"]
            metadata = session["metadata"]

            # Customer and organization info
            customer_email = customer_details["email"]
            customer_name = customer_details["name"]
            customer_id = session["customer"]
            organization_id = metadata["organization_id"]
            organization_name = metadata["organization_name"]
            credits = int(metadata["credits"])
            plan = f"Pro - {credits}"

            # Session info
            subscription_id = session["subscription"]
            payment_status = session["payment_status"]
            amount_total = session["amount_total"]
            currency = session["currency"]
            created_at = datetime.fromtimestamp(session["created"])

            # Retrieve subscription details from Stripe
            try:
                subscription = await asyncio.to_thread(
                    stripe.Subscription.retrieve, subscription_id
                )

                # Extract subscription-specific details from items.data[0]
                subscription_item = subscription["items"]["data"][0]
                current_period_start = datetime.fromtimestamp(
                    subscription_item["current_period_start"]
                )
                current_period_end = datetime.fromtimestamp(
                    subscription_item["current_period_end"]
                )
                subscription_status = subscription["status"]

                # Initialize Supabase client
                supabase = await get_supabase_client()

                # Update organization subscription details
                await update_organization_subscription(
                    supabase=supabase,
                    organization_id=organization_id,
                    customer_id=customer_id,
                    credits=credits,
                    subscription_id=subscription_id,
                    subscription_status=subscription_status,
                    plan=plan,
                    current_period_start=current_period_start,
                    current_period_end=current_period_end,
                )

            except stripe.error.StripeError as e:
                logger.error(f"Error retrieving subscription: {str(e)}")
                raise HTTPException(
                    status_code=500, detail="Error retrieving subscription details"
                )

        except KeyError as e:
            logger.error(f"Missing required field in webhook data: {str(e)}")
            logger.error(f"Available data: {json.dumps(session, indent=2)}")
            raise HTTPException(status_code=400, detail=f"Missing field: {str(e)}")
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}")
            logger.error(
                f"Full error context: {json.dumps(locals(), default=str, indent=2)}"
            )
            raise HTTPException(status_code=500, detail=str(e))

    else:
        logger.info(f"Ignoring event type: {event['type']}")

    return {"status": "success"}


@app.post("/webhook/{webhook_id}")
async def receive_webhook(
    webhook_id: str,
    payload: WebhookPayload,
    request: Request,
    # Dual rate limiting: 1 req/sec per IP AND 1 req/sec per webhook_id
    _ip_limit=Depends(RateLimiter(times=1, seconds=1, identifier=ip_identifier)),
    _webhook_limit=Depends(
        RateLimiter(times=1, seconds=1, identifier=webhook_id_identifier)
    ),
):
    """
    Receive and process webhook data with dual rate limiting:
    - 1 request per second per IP (across all webhooks)
    - 1 request per second per webhook ID (across all IPs)
    Both limits must be satisfied for request to proceed.
    """
    try:
        # Validate webhook_id format
        try:
            uuid.UUID(webhook_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid webhook ID format")

        # Log incoming webhook
        client_host = request.client.host if request.client else "unknown"
        logger.info(f"Received webhook {webhook_id} from {client_host}")

        # Process the webhook data
        processing_result = await process_webhook_data(webhook_id, payload.data)

        # Prepare response
        response_data = {
            "webhook_id": webhook_id,
            "status": "success" if processing_result.get("success") else "error",
            "processing_result": processing_result,
        }

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing webhook {webhook_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
